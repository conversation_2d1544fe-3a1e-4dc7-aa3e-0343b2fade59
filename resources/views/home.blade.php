@extends('layout.app')

@php
    use Illuminate\Support\Facades\Session;
@endphp

@section('title', 'Home')

@section('content')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <style>
        /* --- Horizontal Scroll Section Styling (from landing.blade.php) --- */
        .scroll-section-container {
            position: relative;
        }

        .service-categories-scroll-container {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;  /* Firefox */
        }

        .horizontal-scroll-container {
            display: flex;
            overflow-x: auto;
            white-space: nowrap;
            padding: 16px 0; /* Add padding to the top/bottom */
            gap: 1rem;
            scroll-snap-type: x mandatory;
            min-height: 320px; /* Ensure container has height */
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;  /* Firefox */
        }

        .horizontal-scroll-container::-webkit-scrollbar {
            display: none; /* Chrome, Safari */
        }

        .scroll-item {
            flex: 0 0 auto; /* Prevent item from shrinking */
            width: 280px;  /* Define the width of each card */
            scroll-snap-align: start;
            margin-bottom: 5px;
        }

        /* Scroll Navigation Buttons */
        .scroll-nav-button {
            position: absolute;
            top: calc(50% - 20px); /* Adjust vertical centering */
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.95);
            border: 1px solid #ddd;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        .scroll-nav-button.left { left: -20px; }
        .scroll-nav-button.right { right: -20px; }
        .scroll-nav-button:hover {
            background-color: #fff;
            box-shadow: 0 2px 7px rgba(0, 0, 0, 0.2);
            color: #b30549;
        }

        /* --- Updated Service Card Styling (to match screenshot) --- */
        .service-card {
            position: relative;
            width: 100%;
            height: 320px; /* Adjust height for the new design */
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: block; /* Ensure it behaves as a block element */
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }

        .service-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .service-details {
            position: absolute;
            bottom: 8px; /* Margin from the bottom */
            left: 8px;
            right: 8px;
            background-color: rgba(255, 255, 255, 0.98); /* More opaque */
            backdrop-filter: blur(2px);
            border-radius: 6px;
            padding: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .service-name {
            font-size: 1rem;
            font-weight: bold;
            color: #b30549; /* Brand color for title */
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .service-info {
            font-size: 0.8rem;
            color: #555;
            margin-bottom: 8px;
        }

        .service-meta {
            font-size: 0.75rem;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .service-meta .rating {
            display: flex;
            align-items: center;
        }
        .service-meta .rating .fa-star {
            color: #ffc107;
            margin-right: 4px;
        }

        .book-now-button {
            display: block; /* Make it a block to fill width */
            margin-top: 10px;
            background-color: #b30549;
            color: white;
            text-align: center;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: bold;
            text-decoration: none;
            width: 100%;
        }
        .book-now-button:hover {
            background-color: #8e0439;
            color: white;
        }

        /* --- Category Pills Styling (for brand consistency) --- */
        #service-categories {
            padding-bottom: 10px;
            white-space: nowrap;
        }

        @media (max-width: 425px) {

            #service-categories {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: none;  /* Firefox */
            }

            #service-categories::-webkit-scrollbar {
                display: none; /* Chrome, Safari */
            }

        }


        .category-pill {
            --brand-color: #b30549;
            color: var(--brand-color);
            border-color: var(--brand-color);
            border-radius: 20px !important;
            padding: 0.375rem 1.25rem;
            white-space: nowrap !important;
        }
        .category-pill:hover,
        .category-pill.active {
            background-color: var(--brand-color) !important;
            color: white !important;
            border-color: var(--brand-color) !important;
        }
        .category-pill:focus {
            box-shadow: 0 0 0 0.25rem rgba(179, 5, 73, 0.5);
        }
        .text-maroon {
            color: #b30549 !important;
        }
        #loading-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 320px; /* Match card height */
            background-color: #f8f9fa;
        }

        /* --- Search Form Styling (Landing Page Style) --- */
        .search-section {
            --brand-primary-color: #b30549;
            --brand-secondary-color: #f8f9fa;
            --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .search-section .text-maroon {
            color: var(--brand-primary-color);
        }

        .search-section .btn-primary {
            background-color: var(--brand-primary-color);
            border-color: var(--brand-primary-color);
        }

        .search-section .btn-primary:hover,
        .search-section .btn-primary:focus,
        .search-section .btn-primary:active {
            background-color: #8e0439;
            border-color: #8e0439;
            box-shadow: 0 0 0 0.25rem rgba(179, 5, 73, 0.5);
        }

        .search-section #home-search-form .input-group {
            box-shadow: var(--card-shadow);
            border-radius: 0.375rem;
            overflow: hidden;
        }

        .search-section #home-search-form .form-control,
        .search-section #home-search-form .input-group-text {
            border: none;
            height: calc(1.5em + 1rem + 2px);
        }

        .search-section #home-search-form .form-control:focus {
            box-shadow: none;
            border: none;
            z-index: 3;
        }

        .search-section #home-search-form .input-group-text {
            background-color: white;
        }

        /* Mobile responsiveness for search form */
        @media (max-width: 768px) {
            .search-section .col-lg-5,
            .search-section .col-lg-4,
            .search-section .col-lg-2 {
                margin-bottom: 0.75rem;
            }

            .search-section .col-lg-2 {
                margin-bottom: 0;
            }

            .search-section .display-6 {
                font-size: 2rem;
            }
        }

        /* --- Vendor and Professional Card Styling (from landing page) --- */
        .scroll-item, .pro-scroll-item {
            flex: 0 0 auto;
            scroll-snap-align: start;
            margin-bottom: 5px; /* Space for shadow */
        }
        .scroll-item { width: 280px; } /* Vendor card width */
        .pro-scroll-item { width: 180px; } /* Professional card width */

        /* --- Card Styling Adjustments --- */
        .card {
            border: none;
            box-shadow: var(--card-shadow);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border-radius: 0.5rem;
            height: 100%; /* Make cards fill height in flex item */
        }
        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--card-shadow-hover);
        }
        .card .card-img-top {
            border-bottom: 1px solid #eee;
            border-radius: 0.5rem;
            display: block; /* Prevent potential bottom space */
        }
        /* Vendor card image height */
        .scroll-item .card-img-top {
            height: 320px;
            object-fit: cover;
        }
        /* Vendor card overlay */
        .vendor-card-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255,255,255,0.98); /* Slightly less transparent */
            margin: 8px;
            padding: 10px 12px;
            border-top: 1px solid #eee;
            backdrop-filter: blur(2px);
            border-radius: 0.5rem;
            min-height: 85px; /* Ensure space for content */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* Add shadow to make it appear floating */
        }
        /* Professional card image height */
        .pro-scroll-item .card-img-top {
            width: 90px !important; /* Fixed size for pro image */
            height: 90px !important;
            object-fit: cover;
            margin-top: 0.75rem; /* Space above image */
        }
        .pro-scroll-item .card-body {
            padding: 0.5rem 0.75rem 0.75rem; /* Adjust padding */
        }
        /* Loading/Empty State Text */
        .loading-text, .empty-text {
            color: #6c757d; /* Bootstrap muted color */
        }

        /* View More Button Styling */
        .view-more-button .card {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 320px;
            cursor: pointer;
            background-color: rgba(179, 5, 73, 0.03);
            transition: all 0.3s ease;
        }
        .view-more-button .card:hover {
            background-color: rgba(179, 5, 73, 0.08);
        }
        .view-more-button .fa-arrow-right-long {
            color: var(--brand-primary-color);
            transition: transform 0.3s ease;
        }
        .view-more-button .card:hover .fa-arrow-right-long {
            transform: translateX(5px);
        }
        .loading-indicator .card {
            min-height: 320px;
            background-color: rgba(0, 0, 0, 0.02);
        }

        /* --- Horizontal Scroll Container Styling --- */
        .horizontal-scroll-container {
            display: flex;
            overflow-x: auto;
            scroll-behavior: smooth;
            scroll-snap-type: x mandatory;
            gap: 1rem;
            padding: 0.5rem 0;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        .horizontal-scroll-container::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        /* --- Scroll Navigation Buttons --- */
        .scroll-nav-button {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 50%;
            width: 38px;
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 10;
        }
        .scroll-nav-button:hover {
            background: var(--brand-primary-color);
            color: white;
            border-color: var(--brand-primary-color);
            transform: scale(1.05);
        }
        .scroll-nav-button i {
            font-size: 0.875rem;
        }

        /* --- Section Container Styling --- */
        .scroll-section-container {
            position: relative;
        }
        .scroll-section-container h2 {
            color: var(--brand-primary-color);
        }

        /* Banner Advertisement Styling */
        .banner-carousel {
            position: relative;
            width: 100%;
            height: 200px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .banner-slides {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .banner-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }

        .banner-slide.active {
            opacity: 1;
        }

        .banner-content {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .banner-content img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .banner-overlay {
            position: absolute;
            top: 8px;
            right: 8px;
        }

        .banner-label {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
        }

        .banner-navigation {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .banner-nav-btn {
            background: rgba(255, 255, 255, 0.8);
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .banner-nav-btn:hover {
            background: rgba(255, 255, 255, 1);
        }

        .banner-dots {
            display: flex;
            gap: 5px;
        }

        .banner-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: background 0.3s;
        }

        .banner-dot.active {
            background: white;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .banner-carousel {
                height: 150px;
            }
        }
    </style>

    <div class="mt-5">
        @if(session('auth.logged_in'))
            <div class="container">
                <div class="row">
                    <!-- Profile Completion Card (conditionally displayed) -->
                    @if(Session::get('profile_incomplete'))
                    <div class="col-12 mb-4">
                        <div class="card border-warning" style="max-width: 400px; margin: 0 auto;">
                            <div class="card-header bg-warning text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Complete Your Profile</h5>
                                <button type="button" class="btn-close btn-close-white" aria-label="Close" id="dismiss-profile-card"></button>
                            </div>
                            <div class="card-body">
                                <p class="card-text">Please provide the missing information to enhance your experience.</p>
                                <form id="profile-completion-form" action="{{ route('profile.update') }}" method="POST">
                                    @csrf
                                    <div class="mb-2">
                                        <label for="firstname" class="form-label small">First Name</label>
                                        <input type="text" class="form-control form-control-sm" id="firstname" name="firstname" value="{{ session('firstname') }}" required>
                                    </div>
                                    <div class="mb-2">
                                        <label for="lastname" class="form-label small">Last Name</label>
                                        <input type="text" class="form-control form-control-sm" id="lastname" name="lastname" value="" required>
                                    </div>
                                    <div class="mb-2">
                                        <label for="user_email" class="form-label small">Email</label>
                                        <input type="email" class="form-control form-control-sm" id="user_email" name="user_email" required>
                                    </div>
                                    <div class="mb-2">
                                        <label for="gender" class="form-label small">Gender</label>
                                        <select class="form-select form-select-sm" id="gender" name="gender" required>
                                            <option value="">Select</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    <div class="d-grid gap-2 mt-3">
                                        <button type="submit" class="btn btn-warning btn-sm">Save Profile</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>        
        @endif
    </div>

    <!-- Hero Section -->
    <div class="container mt-5">
        {{-- Check for flashed booking success data and include the partial --}}
        @php
            // Check for booking success in URL parameters
            $bookingSuccess = request()->query('booking_success') === 'true';
            $bookingReference = request()->query('booking_reference');
            $bookingMessage = request()->query('booking_message');

            // Check for booking success in cookies
            $cookieSuccess = request()->cookie('booking_success') === 'true';
            $cookieReference = request()->cookie('booking_reference');
            $cookieMessage = request()->cookie('booking_message');

            // Check for booking success in session (both flash and persistent)
            $flashInfo = session('booking_success_data');
            $persistentInfo = session('booking_success_data_persistent');

            // Set flags based on any of the above sources
            $showBookingSuccess = $bookingSuccess || $cookieSuccess || $flashInfo || $persistentInfo;
            $reference = $bookingReference ?? $cookieReference ??
                        ($flashInfo['reference'] ?? ($persistentInfo['reference'] ?? null));
            $message = $bookingMessage ?? $cookieMessage ??
                      ($flashInfo['message'] ?? ($persistentInfo['message'] ?? 'Booking successful!'));

            // If we found booking success data in cookies or persistent session, clear it
            // This ensures it's only shown once
            if ($cookieSuccess || $persistentInfo) {
                \Illuminate\Support\Facades\Cookie::queue(\Illuminate\Support\Facades\Cookie::forget('booking_success'));
                \Illuminate\Support\Facades\Cookie::queue(\Illuminate\Support\Facades\Cookie::forget('booking_reference'));
                \Illuminate\Support\Facades\Cookie::queue(\Illuminate\Support\Facades\Cookie::forget('booking_message'));
                session()->forget('booking_success_data_persistent');
            }

            // Debug logging
            \Illuminate\Support\Facades\Log::debug('Home page booking success check:', [
                'url_success' => $bookingSuccess,
                'url_reference' => $bookingReference,
                'cookie_success' => $cookieSuccess,
                'cookie_reference' => $cookieReference,
                'cookie_message' => $cookieMessage,
                'flash_info' => $flashInfo,
                'persistent_info' => $persistentInfo,
                'show_booking_success' => $showBookingSuccess,
                'reference' => $reference,
                'message' => $message,
                'all_cookies' => request()->cookies->all(),
                'all_query_params' => request()->query(),
                'session_id' => session()->getId(),
                'is_authenticated' => session('auth.logged_in') ? 'Yes' : 'No',
                'firebase_uid' => session('firebase_uid')
            ]);

            // Debug output removed
        @endphp

        @if ($showBookingSuccess && $reference)
            {{-- Display booking success widget --}}
            @include('partials._booking_success_widget', [
                'reference' => $reference,
                'message'   => $message
            ])
            {{-- Add script to show toast notification --}}
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Create toast container if it doesn't exist
                    let toastContainer = document.querySelector('.toast-container');
                    if (!toastContainer) {
                        toastContainer = document.createElement('div');
                        toastContainer.className = 'toast-container position-fixed top-0 start-50 translate-middle-x p-3';
                        toastContainer.style.zIndex = '1080';
                        document.body.appendChild(toastContainer);
                    }

                    // Create toast element
                    const toast = document.createElement('div');
                    toast.className = 'toast align-items-center text-white bg-success border-0';
                    toast.setAttribute('role', 'alert');
                    toast.setAttribute('aria-live', 'assertive');
                    toast.setAttribute('aria-atomic', 'true');

                    toast.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="fa-solid fa-circle-check me-2"></i>
                                Booking Confirmed! Reference: {{ $reference ?? 'N/A' }}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
`;
                    toastContainer.appendChild(toast);

                    // Initialize and show the toast
                    const bsToast = new bootstrap.Toast(toast, {
                        animation: true,
                        autohide: true,
                        delay: 5000
                    });
                    bsToast.show();
                });
            </script>
        @endif

        {{-- Display other standard flash messages (success, error, etc.) if needed --}}
        @include('partials._flash_messages')

        {{-- Debug section (only visible in local environment) --}}
        @if(app()->environment('local'))
            <div class="mt-4 p-3 bg-light d-none">
                <h5>Debug Information (Booking Success):</h5>
                <p>Show Booking Success: {{ $showBookingSuccess ? 'Yes' : 'No' }}</p>
                <p>Reference: {{ $reference ?? 'None' }}</p>
                <p>Message: {{ $message ?? 'None' }}</p>
                <hr>
                <p>URL Booking Success: {{ $bookingSuccess ? 'Yes' : 'No' }}</p>
                <p>URL Booking Reference: {{ $bookingReference ?? 'None' }}</p>
                <p>URL Booking Message: {{ $bookingMessage ?? 'None' }}</p>
                <hr>
                <p>Cookie Booking Success: {{ $cookieSuccess ? 'Yes' : 'No' }}</p>
                <p>Cookie Booking Reference: {{ $cookieReference ?? 'None' }}</p>
                <p>Cookie Booking Message: {{ $cookieMessage ?? 'None' }}</p>
                <hr>
                <p>Session Flash Data: {{ $flashInfo ? 'Yes' : 'No' }}</p>
                <p>Session Persistent Data: {{ $persistentInfo ? 'Yes' : 'No' }}</p>
                <hr>
                <p>Is Authenticated: {{ session('auth.logged_in') ? 'Yes' : 'No' }}</p>
                <p>Firebase UID: {{ session('firebase_uid') ?? 'None' }}</p>
                <hr>
                <p>All Cookies:</p>
                <pre>{{ json_encode(request()->cookies->all(), JSON_PRETTY_PRINT) }}</pre>
                <p>All Query Parameters:</p>
                <pre>{{ json_encode(request()->query(), JSON_PRETTY_PRINT) }}</pre>
                <p>Full URL: {{ request()->fullUrl() }}</p>
            </div>
        @endif
    </div>

    <!-- Search Section -->
    @if(session('auth.logged_in'))
    <div class="container mt-4 mb-5 search-section">
        <div class="row">
            <div class="col-lg-12 col-12">
                <!-- Hero-style Search Section -->
                <div class="mb-4">
                    <h1 class="fw-bold text-maroon display-6">Discover and Book</h1>
                    <p class="lead text-muted">The best local Hair, Beauty and Wellness experiences near you.</p>
                </div>

                <!-- Search Form -->
                <form action="{{ route('search') }}" method="GET" id="home-search-form" class="mb-4">
                    <div class="row g-3 ">
                        <!-- Service Search Input -->
                        <div class="col-lg-5 col-md-6">
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-white border-end-0 ps-3">
                                    <i class="fa-solid fa-magnifying-glass text-maroon"></i>
                                </span>
                                <input type="text" name="q" id="home-query"
                                       class="form-control form-control-lg border-start-0"
                                       placeholder="Search for services, salons or stylists"
                                       aria-label="Search query" autocomplete="off">
                            </div>
                        </div>

                        <!-- Location Display -->
                        <div class="col-lg-4 col-md-4">
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-white border-end-0 ps-3">
                                    <i class="fa-solid fa-location-dot text-maroon"></i>
                                </span>
                                <input type="text" id="home-user-location"
                                       class="form-control form-control-lg border-start-0"
                                       placeholder="Nairobi"
                                       aria-label="Current location" readonly>
                            </div>
                        </div>

                        <!-- Search Button -->
                        <div class="col-lg-2 col-md-2">
                            <button type="submit" class="btn btn-primary btn-lg w-100 px-4">
                                Search
                            </button>
                        </div>
                    </div>

                    <!-- Hidden fields for coordinates -->
                    <input type="hidden" name="lat" id="home-user-lat" value="">
                    <input type="hidden" name="lng" id="home-user-lng" value="">
                </form>
            </div>
        </div>
    </div>
    @endif

    <!-- Nearby Services Section -->
    @if(session('auth.logged_in') && isset($nearbyServices) && count($nearbyServices) > 0)
    <div class="container mt-5">
        <h2 class="text-maroon fw-bold mb-4">Nearby Services</h2>

        <!-- Service Categories Pills -->
        <div class="d-flex overflow-auto mb-4 no-scrollbar" id="service-categories">
            <button class="btn btn-outline-primary category-pill me-2 active" data-category="all"><span>All Services</span></button>
            @foreach($serviceCategories as $category)
                <button class="btn btn-outline-primary category-pill me-2" data-category="{{ $category['id'] }}"><span>{{ $category['name'] }}</span></button>
            @endforeach
        </div>

        <!-- Service Cards -->
        <div class="scroll-section-container">
            <div class="position-relative">
                <!-- Scroll Buttons -->
                <button class="scroll-nav-button left" id="nearby-services-left-btn" style="display: none;" aria-label="Scroll left">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="scroll-nav-button right" id="nearby-services-right-btn" style="display: none;" aria-label="Scroll right">
                    <i class="fas fa-chevron-right"></i>
                </button>

                <!-- Scrollable Container -->
                <div id="service-cards" class="horizontal-scroll-container">
                    {{-- Render initial services --}}
                    @if(count($nearbyServices) > 0)
                        @foreach($nearbyServices as $service)
                            {{-- NEW: Changed class from 'col' to 'scroll-item' --}}
                            <div class="scroll-item service-item" data-category="{{ $service['categoryID'] ?? $service['category_id'] }}">
                                {{-- NEW: Card structure updated to match target UI --}}
                                <div class="service-card">
                                    <img src="{{ $service['vendor_image'] ?? asset('assets/images/default-vendor.png') }}"
                                         alt="{{ $service['serviceName'] ?? 'Service' }}"
                                         class="service-image">
                                    <div class="service-details">
                                        <div class="service-name">{{ $service['serviceName'] ?? 'Service' }}</div>
                                        <div class="service-info">
                                            Ksh {{ number_format($service['price'] ?? 0, 0) }} for {{ $service['formatted_duration'] ?? '' }}
                                        </div>
                                        <div class="service-meta">
                                            <div class="rating">
                                                {{-- Placeholder for Pro Name --}}
                                                <span>Pro: {{ $service['vendor_name'] ?? 'Vendor' }}</span>
                                                <i class="fa-solid fa-star ms-2"></i>
                                                <span class="fw-bold">{{ number_format($service['vendor_rating'] ?? 0, 1) }}</span>
                                                <span>({{ $service['review_count'] ?? 0 }} reviews)</span>
                                            </div>
                                            {{-- Placeholder for distance --}}
                                            <div class="distance">
                                                <i class="fa-solid fa-location-dot fa-xs me-1"></i>
                                                <span>{{ $service['vendor_distance'] ?? 'N/A' }}</span>
                                            </div>
                                        </div>
                                        <a href="{{ route('booking.initiate', [
                                                    'vendor_id' => $service['vendor_id'] ?? '',
                                                    'service_id' => $service['id'] ?? '',
                                                    'staff_id' => null
                                                ]) }}" class="book-now-button">
                                            BOOK NOW
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="w-100 text-center p-5">
                            <p class="text-muted">No nearby services found.</p>
                        </div>
                    @endif
                    <!-- Loading Indicator will be appended here by JS -->
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Recommended Vendors Section -->
    @if(session('auth.logged_in'))
    <div class="container scroll-section-container mt-5 mb-5">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h2 class="fw-bold text-maroon mb-0">Recommended For You</h2>
        </div>
        <p class="text-muted mb-3">Explore our top-rated and newest vendors for the best services.</p>

        {{-- Scrollable Container with Buttons --}}
        <div class="position-relative">
            {{-- Scroll Buttons --}}
            <button class="scroll-nav-button" id="featured-vendors-left-button"
                    style="position: absolute; left: -19px; top: 50%; transform: translateY(-50%); display: none; z-index: 100;"
                    aria-label="Scroll left">
                <i class="fa-solid fa-chevron-left"></i>
            </button>
            <button class="scroll-nav-button" id="featured-vendors-right-button"
                    style="position: absolute; right: -19px; top: 50%; transform: translateY(-50%); display: none; z-index: 100;"
                    aria-label="Scroll right">
                <i class="fa-solid fa-chevron-right"></i>
            </button>

            {{-- Scrollable Container --}}
            <div id="featured-vendors-scroll" class="horizontal-scroll-container">
                {{-- Loading State (Initial) --}}
                <div class="w-100 text-center p-5">
                    <div class="spinner-border text-maroon" role="status">
                        <span class="visually-hidden">Loading vendors...</span>
                    </div>
                    <p class="mt-2 loading-text">Loading recommendations...</p>
                </div>
                {{-- Vendor cards dynamically added by JS --}}
            </div>
        </div>
    </div>
    @endif

    {{-- Banner Advertisement Section --}}
    @if(session('auth.logged_in'))
    <div class="container mt-5 mb-5">
        <div id="banner-container" style="display: none;">
            {{-- Banner carousel will be dynamically loaded here --}}
        </div>
    </div>
    @endif

    <!-- Popular Professionals Section -->
    @if(session('auth.logged_in'))
    <div class="container scroll-section-container mt-5 mb-5">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h2 class="fw-bold text-maroon mb-0">Popular Professionals</h2>
            <button type="button" id="view-all-professionals"
               class="btn btn-sm btn-outline-secondary">
                View All
            </button>
        </div>
        <p class="text-muted mb-3">Meet our most sought-after beauty and wellness experts.</p>

        {{-- Scroll Navigation Buttons --}}
        <div class="position-relative">
            <button class="scroll-nav-button" id="popular-pro-left-button"
                    style="position: absolute; left: -19px; top: 50%; transform: translateY(-50%); display: none; z-index: 100;"
                    aria-label="Scroll left">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="scroll-nav-button" id="popular-pro-right-button"
                    style="position: absolute; right: -19px; top: 50%; transform: translateY(-50%); display: none; z-index: 100;"
                    aria-label="Scroll right">
                <i class="fas fa-chevron-right"></i>
            </button>

            {{-- Scrollable Container --}}
            <div id="popular-professionals-scroll"
                 class="horizontal-scroll-container"
                 data-type="professionals">
                {{-- Loading State (Initial) --}}
                <div class="w-100 text-center p-5">
                    <div class="spinner-border text-maroon" role="status">
                        <span class="visually-hidden">Loading professionals...</span>
                    </div>
                    <p class="mt-2 loading-text">Finding popular professionals...</p>
                </div>
            </div>
        </div>
    </div>
    @endif
@endsection

@section('scripts')
<!-- Google Maps API for Geocoding -->
<script src="https://maps.googleapis.com/maps/api/js?key={{ $googleApiKey }}&libraries=geometry,places&v=weekly" async defer></script>

<!-- Search Form JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize search form functionality
    initializeSearchForm();
});

function initializeSearchForm() {
    const locationInput = document.getElementById('home-user-location');
    const latInput = document.getElementById('home-user-lat');
    const lngInput = document.getElementById('home-user-lng');

    if (!locationInput || !latInput || !lngInput) {
        console.warn('Search form elements not found');
        return;
    }

    // Try to get location from session data first (passed from controller)
    @if(Session::get('user_location.lat') && Session::get('user_location.lng'))
        const sessionLat = {{ Session::get('user_location.lat') }};
        const sessionLng = {{ Session::get('user_location.lng') }};

        if (sessionLat && sessionLng) {
            console.log('Using session location:', sessionLat, sessionLng);
            updateLocationFields({ lat: sessionLat, lng: sessionLng });
        } else {
            // Fallback to geolocation
            getUserLocation();
        }
    @else
        // No session location, try geolocation
        getUserLocation();
    @endif
}

function getUserLocation() {
    const locationInput = document.getElementById('home-user-location');
    if (!locationInput) return;

    locationInput.value = 'Detecting location...';
    locationInput.disabled = true;

    if (navigator.geolocation) {
        const geoOptions = {
            enableHighAccuracy: true,
            timeout: 8000,
            maximumAge: 60000
        };

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const coords = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                console.log('Geolocation success:', coords);
                updateLocationFields(coords);
                locationInput.disabled = false;
            },
            (error) => {
                console.error('Geolocation error:', error.message);
                handleLocationError();
                locationInput.disabled = false;
            },
            geoOptions
        );
    } else {
        console.log("Geolocation is not supported.");
        handleLocationError();
        locationInput.disabled = false;
    }
}

function updateLocationFields(coords) {
    const locationInput = document.getElementById('home-user-location');
    const latInput = document.getElementById('home-user-lat');
    const lngInput = document.getElementById('home-user-lng');

    if (!locationInput || !latInput || !lngInput) return;

    // Update hidden coordinate fields
    latInput.value = coords.lat;
    lngInput.value = coords.lng;

    // Reverse geocode to get address (optional)
    reverseGeocode(coords.lat, coords.lng);
}

function reverseGeocode(lat, lng) {
    const locationInput = document.getElementById('home-user-location');
    if (!locationInput) return;

    // Set placeholder while geocoding
    locationInput.placeholder = 'Fetching address...';

    // Function to perform geocoding
    function performGeocode() {
        if (typeof google !== 'undefined' && google.maps && google.maps.Geocoder) {
            const geocoder = new google.maps.Geocoder();
            const latlng = { lat: parseFloat(lat), lng: parseFloat(lng) };

            geocoder.geocode({ 'location': latlng }, (results, status) => {
                if (status === 'OK' && results?.[0]) {
                    // Use the full formatted address
                    locationInput.value = results[0].formatted_address;
                } else {
                    // Fallback to default location name if geocoding fails
                    fallbackToLocationName(lat, lng);
                }
            });
        } else {
            // If Google Maps API is not available, use default location names
            fallbackToLocationName(lat, lng);
        }
    }

    // Fallback function for location names
    function fallbackToLocationName(lat, lng) {
        if (Math.abs(lat - (-1.286389)) < 0.1 && Math.abs(lng - 36.817223) < 0.1) {
            locationInput.value = 'Nairobi, Kenya';
        } else {
            // For other locations, show a simplified coordinate format
            locationInput.value = `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
        }
    }

    // Wait for Google Maps API to load if it's not ready yet
    if (typeof google === 'undefined' || !google.maps) {
        // Check every 100ms for up to 5 seconds
        let attempts = 0;
        const maxAttempts = 50;
        const checkInterval = setInterval(() => {
            attempts++;
            if (typeof google !== 'undefined' && google.maps && google.maps.Geocoder) {
                clearInterval(checkInterval);
                performGeocode();
            } else if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                console.warn('Google Maps API not loaded after 5 seconds, using fallback');
                fallbackToLocationName(lat, lng);
            }
        }, 100);
    } else {
        performGeocode();
    }
}

function handleLocationError() {
    const locationInput = document.getElementById('home-user-location');
    const latInput = document.getElementById('home-user-lat');
    const lngInput = document.getElementById('home-user-lng');

    if (!locationInput || !latInput || !lngInput) return;

    // Use default location (Nairobi)
    const defaultLat = -1.286389;
    const defaultLng = 36.817223;

    // Set coordinates
    latInput.value = defaultLat;
    lngInput.value = defaultLng;

    // Try to get a proper address for the default location
    reverseGeocode(defaultLat, defaultLng);

    console.log('Using default location: Nairobi');
}

// Initialize vendors and professionals sections (Landing Page Implementation)
function initializeVendorsAndProfessionals() {
    // Get location from search form or session
    const latInput = document.getElementById('home-user-lat');
    const lngInput = document.getElementById('home-user-lng');

    let lat, lng;

    // Try to get coordinates from search form
    if (latInput && lngInput && latInput.value && lngInput.value) {
        lat = parseFloat(latInput.value);
        lng = parseFloat(lngInput.value);
    } else {
        // Use session location or default
        @if(Session::get('user_location.lat') && Session::get('user_location.lng'))
            lat = {{ Session::get('user_location.lat') }};
            lng = {{ Session::get('user_location.lng') }};
        @else
            lat = -1.286389; // Default Nairobi
            lng = 36.817223;
        @endif
    }

    console.log('Initializing vendors and professionals with location:', lat, lng);

    // Initialize vendor tracking variables (from landing page)
    window.loadedVendors = { recommended: [] };
    window.isLoading = { recommended: false };
    window.hasMoreVendors = { recommended: true };
    window.VENDOR_THRESHOLD = 60; // Show View More button after this many vendors
    window.VENDORS_PER_PAGE = 20; // Number of vendors to load per request

    // Store location globally for vendor functions
    window.currentLat = lat;
    window.currentLng = lng;

    // Get container elements
    window.vendorContainer = document.getElementById('featured-vendors-scroll');
    window.vendorBtnLeft = document.getElementById('featured-vendors-left-button');
    window.vendorBtnRight = document.getElementById('featured-vendors-right-button');

    // Fetch initial vendors
    fetchVendors(lat, lng, 'recommended', false);

    // Fetch professionals (using landing page function)
    fetchPopularProfessionals(lat, lng);

    // Load banner advertisements
    loadBanners(lat, lng);

    // Setup scroll buttons for professionals
    setupScrollButtons('popular-professionals-scroll', 'popular-pro-left-button', 'popular-pro-right-button');
}

// Vendor fetching function (exact copy from landing page)
function fetchVendors(lat, lng, type = 'recommended', append = false) {
    if (isLoading[type]) {
        console.log(`Already loading ${type} vendors, skipping request`);
        return;
    }

    isLoading[type] = true;
    const container = vendorContainer;

    if (!container) {
        console.error('Vendor container not found');
        isLoading[type] = false;
        return;
    }

    // Calculate offset for pagination
    const offset = append ? loadedVendors[type].length : 0;
    console.log(`Fetching ${type} vendors: offset=${offset}, append=${append}, hasMore=${hasMoreVendors[type]}`);

    // Show loading indicator if appending
    if (append && hasMoreVendors[type]) {
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'scroll-item loading-indicator';
        loadingIndicator.innerHTML = `
            <div class="card h-100 d-flex justify-content-center align-items-center">
                <div class="text-center">
                    <div class="spinner-border text-maroon" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 loading-text">Loading more...</p>
                </div>
            </div>
        `;

        // Remove existing View More button if present
        const viewMoreButton = container.querySelector('.view-more-button');
        if (viewMoreButton) {
            container.removeChild(viewMoreButton);
        }

        container.appendChild(loadingIndicator);
    }

    const apiUrl = `{{ route('api.vendors.list') }}?lat=${lat}&lng=${lng}&limit=${VENDORS_PER_PAGE}&offset=${offset}&type=${type}&include_demo=true`;

    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Remove loading indicator
            const loadingIndicator = container.querySelector('.loading-indicator');
            if (loadingIndicator) {
                container.removeChild(loadingIndicator);
            }

            if (data.vendors && Array.isArray(data.vendors)) {
                console.log(`Received ${data.vendors.length} ${type} vendors`);

                if (append) {
                    // Filter out vendors we already have
                    const existingIds = loadedVendors[type].map(v => v.id);
                    const newVendors = data.vendors.filter(vendor => !existingIds.includes(vendor.id));

                    console.log(`Received ${data.vendors.length} vendors, ${newVendors.length} are new (filtered out ${data.vendors.length - newVendors.length} duplicates)`);
                    console.log(`Existing IDs: ${existingIds.length}, New vendor IDs: ${newVendors.map(v => v.id).join(', ')}`);

                    if (newVendors.length > 0) {
                        renderVendorCards(newVendors, type, true);

                        // Update loaded vendors
                        loadedVendors[type] = [...loadedVendors[type], ...newVendors];
                        console.log(`Total loaded vendors: ${loadedVendors[type].length}/${VENDOR_THRESHOLD} for ${type}`);

                        // If we got fewer vendors than requested, we've reached the end
                        if (newVendors.length < VENDORS_PER_PAGE) {
                            hasMoreVendors[type] = false;
                            console.log(`Received ${newVendors.length}/${VENDORS_PER_PAGE} vendors, marking ${type} as complete`);
                        }
                    } else {
                        // If we got no new vendors, assume we've reached the end
                        hasMoreVendors[type] = false;
                        console.log(`No new vendors received, marking ${type} as complete`);
                    }
                } else {
                    renderVendorCards(data.vendors, type, false);

                    // Update loaded vendors
                    loadedVendors[type] = data.vendors;

                    // If we got fewer vendors than requested, we've reached the end
                    if (data.vendors.length < VENDORS_PER_PAGE) {
                        hasMoreVendors[type] = false;
                        console.log(`Initial load: received ${data.vendors.length}/${VENDORS_PER_PAGE} vendors, marking ${type} as complete`);
                    }
                }

                // Add View More button if we've reached the threshold or if we have no more vendors
                // Also add it if we've loaded at least 30 vendors and received fewer than requested (indicating we're near the end)
                if (loadedVendors[type].length >= VENDOR_THRESHOLD ||
                    !hasMoreVendors[type] ||
                    (loadedVendors[type].length >= 30 && data.vendors.length < VENDORS_PER_PAGE)) {
                    console.log(`Adding View More button for ${type} vendors: threshold=${loadedVendors[type].length >= VENDOR_THRESHOLD}, hasMore=${hasMoreVendors[type]}, nearEnd=${loadedVendors[type].length >= 30 && data.vendors.length < VENDORS_PER_PAGE}`);
                    addViewMoreButton(container, type, lat, lng);
                    hasMoreVendors[type] = false; // Stop loading more after adding View More
                } else {
                    console.log(`Loaded ${loadedVendors[type].length}/${VENDOR_THRESHOLD} ${type} vendors, continuing to load more`);
                }

                isLoading[type] = false;

                // Check if we need to setup scroll detection
                if (hasMoreVendors[type]) {
                    setupScrollDetection(container, type);
                }
            } else {
                console.log(`No ${type} vendors received from API`);
                if (!append) {
                    setEmptyState(container, `No vendors found near this location.`);
                }
                isLoading[type] = false;
            }
        })
        .catch(error => {
            console.error(`Error fetching ${type} vendors:`, error);

            // Remove loading indicator
            const loadingIndicator = container.querySelector('.loading-indicator');
            if (loadingIndicator) {
                container.removeChild(loadingIndicator);
            }

            if (!append) {
                setErrorState(container, `Could not load ${type} vendors. Please try again.`);
            }
            isLoading[type] = false;
        });
}

// Professional fetching function (exact copy from landing page)
function fetchPopularProfessionals(lat, lng) {
    const proContainer = document.getElementById('popular-professionals-scroll');
    const proBtnLeft = document.getElementById('popular-pro-left-button');
    const proBtnRight = document.getElementById('popular-pro-right-button');

    if (!proContainer || !proBtnLeft || !proBtnRight) {
        console.error("Pro DOM elements missing.");
        return;
    }

    setLoadingState(proContainer, proBtnLeft, proBtnRight, "Finding popular professionals...");

    if (lat === null || lng === null || isNaN(lat) || isNaN(lng)) {
        setErrorState(proContainer, "Please provide a location to see professionals.");
        return;
    }

    const apiUrl = `{{ route('api.professionals.popular') }}?lat=${lat}&lng=${lng}&limit=10`;

    fetch(apiUrl)
        .then(handleFetchResponse)
        .then(data => {
            // console.log("Professionals API Data:", data); // Debug log
            renderProfessionalCards(data.professionals); // Adjust key if needed
        })
        .catch(error => {
            console.error('Error fetching professionals:', error);
            // Provide a slightly different error message
            setErrorState(proContainer, "Could not load popular professionals.");
        });
}

// Utility function for handling fetch responses (from landing page)
function handleFetchResponse(response) {
    if (!response.ok) {
        throw new Error(`Network response was not ok: ${response.status}`);
    }
    return response.json();
}

// Loading state function (from landing page)
function setLoadingState(container, btnLeft, btnRight, loadingText = "Loading...") {
    if (!container) return;

    container.innerHTML = `
        <div class="w-100 text-center p-5">
            <div class="spinner-border text-maroon" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 loading-text">${loadingText}</p>
        </div>
    `;

    if (btnLeft) btnLeft.style.display = 'none';
    if (btnRight) btnRight.style.display = 'none';
}

// Vendor card rendering function (exact copy from landing page)
function renderVendorCards(vendors, type = 'recommended', append = false) {
    // Always use the recommended container since we've merged the sections
    const container = vendorContainer;
    const btnLeft = vendorBtnLeft;
    const btnRight = vendorBtnRight;

    if (!container || !btnLeft || !btnRight) return;

    // Clear container if not appending
    if (!append) {
        container.innerHTML = '';
    }

    if (vendors && Array.isArray(vendors) && vendors.length > 0) {
        console.log(`Rendering ${vendors.length} ${type} vendors, append: ${append}, total: ${loadedVendors[type].length}`);
        // Create a document fragment to minimize DOM operations
        const fragment = document.createDocumentFragment();

        vendors.forEach(vendor => {
            const vendorId = vendor.id;
            const vendorSlug = vendor.vendor_slug;
            if (!vendorId) { console.warn("Skipping vendor with missing ID:", vendor); return; }
            if (!vendorSlug) { console.warn("Vendor missing slug:", vendor.id); }

            const card = document.createElement('div');
            card.className = 'scroll-item mb-3';

            const profileUrl = `/vendor/${encodeURIComponent(vendorSlug || 'vendor')}/${encodeURIComponent(vendorId)}`;
            const avgRating = vendor.average_rating !== undefined ? parseFloat(vendor.average_rating).toFixed(1) : '0.0';
            const reviewCount = vendor.review_count || 0;
            const distance = vendor.distance || null;
            const imageUrl = vendor.vendor_logo || '/assets/images/default-vendor.png';
            const locationText = vendor.vendor_loc || 'Location unavailable';
            const vendorName = vendor.vendor_name || 'No Name';

            // Add 'New' badge for vendors with is_new flag
            const newBadge = vendor.is_new ? `
                <div class="position-absolute top-0 start-0 m-2">
                    <span class="badge bg-success">New</span>
                </div>
            ` : '';

            card.innerHTML = `
                <div class="card h-100 position-relative">
                    ${newBadge}
                    <img src="${imageUrl}" alt="${vendorName} Logo" class="card-img-top" loading="lazy" onerror="this.onerror=null; this.src='/assets/images/default-vendor.png';">
                    <div class="vendor-card-overlay">
                        <h6 class="mb-1 fw-bold text-truncate" title="${vendorName}">${vendorName}</h6>
                        <div class="d-flex justify-content-between align-items-center mb-1 small gap-2">
                            <div class="d-flex align-items-center text-muted">
                                <span class="text-warning me-1"><i class="fa-solid fa-star"></i></span>
                                <span class="fw-bold">${avgRating}</span>
                                <span class="ms-1">(${reviewCount})</span>
                            </div>
                            ${distance ? `<div class="d-flex align-items-center text-muted">
                                <i class="fa-solid fa-location-arrow fa-xs me-1"></i>${distance}
                            </div>` : ''}
                        </div>
                        <div class="text-muted small text-truncate" title="${locationText}">
                            <i class="fa-solid fa-map-marker-alt fa-xs me-1"></i>${locationText}
                        </div>
                    </div>
                    <a href="${profileUrl}" class="stretched-link" aria-label="View profile for ${vendorName}"></a>
                </div>`;
            fragment.appendChild(card);
        });

        container.appendChild(fragment);
        container.style.display = 'flex';
        setupScrollButtons(
            'featured-vendors-scroll',
            'featured-vendors-left-button',
            'featured-vendors-right-button'
        );

        // Setup scroll detection to load more vendors when scrolling
        setupScrollDetection(container, type);
    } else if (!append) {
        console.log(`No ${type} vendors to render.`);
        setEmptyState(container, `No vendors found near this location.`);
    }
}

// Professional card rendering function (exact copy from landing page)
function renderProfessionalCards(professionals) {
    const proContainer = document.getElementById('popular-professionals-scroll');
    const proBtnLeft = document.getElementById('popular-pro-left-button');
    const proBtnRight = document.getElementById('popular-pro-right-button');

    if (!proContainer || !proBtnLeft || !proBtnRight) return;
    proContainer.innerHTML = ''; // Clear

    if (professionals && Array.isArray(professionals) && professionals.length > 0) {
        professionals.forEach(pro => {
            if (!pro || !pro.id) { console.warn("Skipping professional with missing data:", pro); return; }

            const card = document.createElement('div');
            card.className = 'pro-scroll-item mb-3';

            // Extract all needed data with fallbacks
            const proName = pro.full_name || 'Professional';
            const proTitle = pro.specialization || 'Professional';
            const proImage = pro.profile_photo || '/assets/images/default-pro.png';
            const proRating = pro.average_rating ? parseFloat(pro.average_rating).toFixed(1) : null;
            const reviewCount = pro.review_count || 0;
            const profileUrl = `/professional/${encodeURIComponent(pro.id)}`;

            // Vendor details
            const vendorDetails = pro.vendor_details || {};
            const vendorName = vendorDetails.vendor_name || '';
            const vendorAddress = vendorDetails.vendor_address || '';
            const distance = pro.distance_text || '';

            card.innerHTML = `
                <div class="card h-100 shadow-sm">
                    <div class="position-relative">
                        <img src="${proImage}"
                             class="card-img-top rounded-circle mx-auto"
                             alt="${proName}"
                             loading="lazy"
                             onerror="this.onerror=null; this.src='/assets/images/default-pro.png';">
                        ${proRating ? `
                            <div class="position-absolute top-0 end-0 m-2 bg-white rounded-pill px-2 py-1 shadow-sm">
                                <span class="text-warning"><i class="fa-solid fa-star"></i></span>
                                <span class="fw-bold">${proRating}</span>
                                <span class="text-muted small">(${reviewCount})</span>
                            </div>
                        ` : ''}
                    </div>
                    <div class="card-body text-center">
                        <h6 class="card-title mb-1 fw-bold text-truncate" title="${proName}">
                            ${proName}
                        </h6>
                        <p class="card-text small text-muted mb-2">${proTitle}</p>
                        ${vendorName ? `
                            <a href="/vendor/${encodeURIComponent(vendorDetails.vendor_slug || 'vendor')}"
                               class="small text-truncate d-block mb-1 text-decoration-none"
                               title="${vendorName}">
                                <i class="fa-solid fa-building fa-xs me-1"></i>${vendorName}
                            </a>
                        ` : ''}
                        ${vendorAddress ? `
                            <div class="small text-muted text-truncate" title="${vendorAddress}">
                                <i class="fa-solid fa-location-dot fa-xs me-1"></i>${vendorAddress}
                            </div>
                        ` : ''}
                        ${distance ? `
                            <div class="small text-primary mt-1">
                                <i class="fa-solid fa-location-arrow fa-xs me-1"></i>${distance}
                            </div>
                        ` : ''}
                    </div>
                    <div class="card-footer bg-transparent border-top-0 text-center">
                        <div class="d-flex justify-content-center gap-2">
                            <a href="{{ url('/professional') }}/${pro.id}"
                               class="btn btn-sm btn-outline-primary">
                                View Profile
                            </a>
                            @auth
                                <button class="btn btn-sm btn-outline-danger favorite-btn-landing"
                                        data-staff-id="${pro.id}"
                                        title="Add to favorites">
                                    <i class="far fa-heart"></i>
                                </button>
                            @endauth
                        </div>
                    </div>
                </div>`;

            proContainer.appendChild(card);
        });

        proContainer.style.display = 'flex';
        setupScrollButtons('popular-professionals-scroll', 'popular-pro-left-button', 'popular-pro-right-button');
    } else {
        setEmptyState(proContainer, "No popular professionals found nearby.");
    }
}

// Scroll detection function (exact copy from landing page)
function setupScrollDetection(container, type) {
    // Remove existing scroll handler if any
    if (container._scrollHandler) {
        container.removeEventListener('scroll', container._scrollHandler);
    }

    // Create debounced scroll handler
    container._scrollHandler = debounce(() => {
        if (isLoading[type] || !hasMoreVendors[type]) {
            return;
        }

        const scrollLeft = container.scrollLeft;
        const scrollWidth = container.scrollWidth;
        const clientWidth = container.clientWidth;
        const scrollPercentage = (scrollLeft / (scrollWidth - clientWidth)) * 100;

        // Load more when 80% scrolled
        if (scrollPercentage >= 80) {
            console.log(`Scroll threshold reached for ${type} vendors: ${scrollPercentage.toFixed(1)}%`);
            fetchVendors(currentLat, currentLng, type, true);
        }
    }, 250);

    // Add the scroll handler
    container.addEventListener('scroll', container._scrollHandler, { passive: true });
}

// Function to add View More button (exact copy from landing page)
function addViewMoreButton(container, type, lat, lng) {
    // Check if View More button already exists
    if (container.querySelector('.view-more-button')) {
        return;
    }

    const viewMoreButton = document.createElement('div');
    viewMoreButton.className = 'scroll-item view-more-button';
    viewMoreButton.innerHTML = `
        <div class="card h-100 d-flex justify-content-center align-items-center" style="min-width: 220px;">
            <div class="d-flex flex-column align-items-center p-4">
                <i class="fa-solid fa-arrow-right-long fa-2x mb-3"></i>
                <h5 class="text-center">View More</h5>
                <p class="text-center text-muted small">See all ${type} vendors</p>
            </div>
        </div>
    `;

    // Add click event to redirect to search page
    viewMoreButton.addEventListener('click', function() {
        // Get the current count of loaded vendors to use as starting offset
        const loadedCount = loadedVendors[type].length;
        console.log(`View More clicked with ${loadedCount} vendors already loaded`);

        // Pass the total number of loaded vendors as the startOffset
        // This tells the search page how many vendors the user has already seen
        const startOffset = loadedCount;

        console.log(`View More clicked with ${loadedCount} vendors loaded, startOffset=${startOffset}`);

        // Redirect to search page with appropriate parameters
        const searchUrl = new URL("{{ route('search') }}", window.location.origin);
        searchUrl.searchParams.append('type', type);
        searchUrl.searchParams.append('start_offset', startOffset);
        if (lat && lng) {
            searchUrl.searchParams.append('lat', lat);
            searchUrl.searchParams.append('lng', lng);
        }
        window.location.href = searchUrl.toString();
    });

    container.appendChild(viewMoreButton);
}

// Debounce function (from landing page)
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Utility functions
function setupScrollButtons(containerId, leftButtonId, rightButtonId) {
    const container = document.getElementById(containerId);
    const leftButton = document.getElementById(leftButtonId);
    const rightButton = document.getElementById(rightButtonId);

    console.log('Setting up scroll buttons for', containerId, !!container, !!leftButton, !!rightButton);

    if (!container || !leftButton || !rightButton) {
        console.error('Missing elements for scroll setup:', { container: !!container, leftButton: !!leftButton, rightButton: !!rightButton });
        return;
    }

    // Check if scrolling is needed
    function updateScrollButtons() {
        const canScrollLeft = container.scrollLeft > 0;
        const canScrollRight = container.scrollLeft < (container.scrollWidth - container.clientWidth);

        leftButton.style.display = canScrollLeft ? 'block' : 'none';
        rightButton.style.display = canScrollRight ? 'block' : 'none';
    }

    // Scroll functions
    leftButton.addEventListener('click', () => {
        container.scrollBy({ left: -280, behavior: 'smooth' });
    });

    rightButton.addEventListener('click', () => {
        container.scrollBy({ left: 280, behavior: 'smooth' });
    });

    // Update buttons on scroll
    container.addEventListener('scroll', updateScrollButtons);

    // Initial check (after content loads)
    setTimeout(updateScrollButtons, 500);
}



function setEmptyState(container, message) {
    container.innerHTML = `
        <div class="w-100 text-center p-5">
            <i class="fa-solid fa-search text-muted mb-3" style="font-size: 2rem;"></i>
            <p class="empty-text">${message}</p>
        </div>
    `;
}

function setErrorState(container, message) {
    container.innerHTML = `
        <div class="w-100 text-center p-5">
            <i class="fa-solid fa-exclamation-triangle text-warning mb-3" style="font-size: 2rem;"></i>
            <p class="text-muted">${message}</p>
            <button class="btn btn-sm btn-outline-primary" onclick="location.reload()">
                <i class="fa-solid fa-refresh me-1"></i>
                Try Again
            </button>
        </div>
    `;
}

// Setup View All Professionals button (from landing page)
function setupViewAllProfessionalsButton() {
    const viewAllBtn = document.getElementById('view-all-professionals');
    if (viewAllBtn) {
        viewAllBtn.addEventListener('click', function() {
            let lat, lng;

            // Get coordinates from current location
            if (window.currentLat && window.currentLng) {
                lat = window.currentLat;
                lng = window.currentLng;
            } else {
                // Fallback to input fields
                const latInput = document.getElementById('home-user-lat');
                const lngInput = document.getElementById('home-user-lng');

                if (latInput && lngInput && latInput.value && lngInput.value) {
                    lat = latInput.value;
                    lng = lngInput.value;
                }
            }

            // Create the URL with parameters (exact same as landing page)
            const searchUrl = new URL("{{ route('search') }}", window.location.origin);
            searchUrl.searchParams.append('type', 'professionals');
            searchUrl.searchParams.append('sort', 'popular');
            searchUrl.searchParams.append('q', 'Popular Professionals');
            // Don't set the tab parameter - let the controller determine it from type

            // Add location parameters if available
            if (lat && lng) {
                searchUrl.searchParams.append('lat', lat);
                searchUrl.searchParams.append('lng', lng);
            }

            // Redirect to the search page
            window.location.href = searchUrl.toString();
        });
    }
}

// Update the main initialization to include vendors and professionals
document.addEventListener('DOMContentLoaded', function() {
    // Initialize search form functionality
    initializeSearchForm();

    // Initialize vendors and professionals sections
    setTimeout(() => {
        initializeVendorsAndProfessionals();

        // Setup View All button for professionals
        setupViewAllProfessionalsButton();

        // Setup favorites functionality for professionals
        setupProfessionalFavorites();
    }, 1000); // Wait for search form location to be ready
});

// Setup favorites functionality for professional cards (from landing page)
function setupProfessionalFavorites() {
    // Add event delegation for favorite buttons on professional cards
    document.addEventListener('click', function(e) {
        if (e.target.closest('.favorite-btn-landing')) {
            e.preventDefault();
            e.stopPropagation();

            const button = e.target.closest('.favorite-btn-landing');
            const staffId = button.getAttribute('data-staff-id');

            if (!staffId) {
                console.error('No staff ID found for favorite button');
                return;
            }

            // Check if user is authenticated
            @auth
                toggleFavorite(staffId, button);
            @else
                // Redirect to login if not authenticated
                if (confirm('You need to log in to add favorites. Would you like to go to the login page?')) {
                    window.location.href = '{{ route("auth.showLogin") }}';
                }
            @endauth
        }
    });
}

// Toggle favorite function (simplified version for home page)
function toggleFavorite(staffId, button) {
    const icon = button.querySelector('i');
    const isCurrentlyFavorited = icon.classList.contains('fas');

    // Optimistic UI update
    if (isCurrentlyFavorited) {
        icon.classList.remove('fas');
        icon.classList.add('far');
        button.title = 'Add to favorites';
    } else {
        icon.classList.remove('far');
        icon.classList.add('fas');
        button.title = 'Remove from favorites';
    }

    // Make API call to toggle favorite
    const action = isCurrentlyFavorited ? 'remove' : 'add';

    fetch('{{ route("api.user.favorites.toggle") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify({
            staff_id: staffId,
            action: action
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            // Revert UI change if API call failed
            if (isCurrentlyFavorited) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                button.title = 'Remove from favorites';
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                button.title = 'Add to favorites';
            }
            console.error('Failed to toggle favorite:', data.message);
        }
    })
    .catch(error => {
        // Revert UI change if request failed
        if (isCurrentlyFavorited) {
            icon.classList.remove('far');
            icon.classList.add('fas');
            button.title = 'Remove from favorites';
        } else {
            icon.classList.remove('fas');
            icon.classList.add('far');
            button.title = 'Add to favorites';
        }
        console.error('Error toggling favorite:', error);
    });
}
</script>
@if(Session::get('profile_incomplete'))
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle dismiss button
    const dismissBtn = document.getElementById('dismiss-profile-card');
    if (dismissBtn) {
        dismissBtn.addEventListener('click', function() {
            this.closest('.col-12').style.display = 'none';
        });
    }

    // Handle form submission via AJAX
    const profileForm = document.getElementById('profile-completion-form');
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch(this.action, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Profile update successful:', data);

                    // Check if the profile is now complete
                    const isComplete = !data.missingFields || data.missingFields.length === 0;

                    if (isComplete) {
                        console.log('Profile is now complete, removing incomplete flag');
                        // Remove the incomplete profile flag
                        fetch('/profile/mark-complete', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });
                    } else {
                        console.log('Profile still incomplete:', data.missingFields);
                    }

                    // Hide the card
                    this.closest('.col-12').style.display = 'none';

                    // Show toast notification
                    const toastContainer = document.createElement('div');
                    toastContainer.className = 'toast-container position-fixed top-0 start-50 translate-middle-x p-3';
                    toastContainer.style.zIndex = '1080';

                    const toast = document.createElement('div');
                    toast.className = 'toast align-items-center text-white bg-success border-0';
                    toast.setAttribute('role', 'alert');
                    toast.setAttribute('aria-live', 'assertive');
                    toast.setAttribute('aria-atomic', 'true');

                    toast.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                Profile updated successfully!
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    `;

                    toastContainer.appendChild(toast);
                    document.body.appendChild(toastContainer);

                    // Initialize and show the toast
                    const bsToast = new bootstrap.Toast(toast, {
                        animation: true,
                        autohide: true,
                        delay: 3000
                    });
                    bsToast.show();

                    // Update the displayed name if it changed
                    const welcomeHeading = document.querySelector('h1');
                    if (welcomeHeading) {
                        welcomeHeading.textContent = 'Welcome Home, ' + document.getElementById('firstname').value;
                    }
                } else {
                    // Show error toast
                    const toastContainer = document.createElement('div');
                    toastContainer.className = 'toast-container position-fixed top-0 start-50 translate-middle-x p-3';
                    toastContainer.style.zIndex = '1080';

                    const toast = document.createElement('div');
                    toast.className = 'toast align-items-center text-white bg-danger border-0';
                    toast.setAttribute('role', 'alert');
                    toast.setAttribute('aria-live', 'assertive');
                    toast.setAttribute('aria-atomic', 'true');

                    toast.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                Error updating profile: ${data.message}
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    `;

                    toastContainer.appendChild(toast);
                    document.body.appendChild(toastContainer);

                    // Initialize and show the toast
                    const bsToast = new bootstrap.Toast(toast, {
                        animation: true,
                        autohide: true,
                        delay: 5000
                    });
                    bsToast.show();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Show error toast
                const toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed top-0 start-50 translate-middle-x p-3';
                toastContainer.style.zIndex = '1080';

                const toast = document.createElement('div');
                toast.className = 'toast align-items-center text-white bg-danger border-0';
                toast.setAttribute('role', 'alert');
                toast.setAttribute('aria-live', 'assertive');
                toast.setAttribute('aria-atomic', 'true');

                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            An error occurred. Please try again.
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                `;

                toastContainer.appendChild(toast);
                document.body.appendChild(toastContainer);

                // Initialize and show the toast
                const bsToast = new bootstrap.Toast(toast, {
                    animation: true,
                    autohide: true,
                    delay: 5000
                });
                bsToast.show();
            });
        });
    }
});
</script>
@endif

<!-- Service Category Filtering and Loading Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const serviceCardsContainer = document.getElementById('service-cards');
        if (!serviceCardsContainer) return; // Exit if the container is not on the page

        // --- State Variables ---
        let currentPage = 1;
        let isLoading = false;
        let selectedCategory = 'all';
        let hasMoreServices = true;

        // --- Helper Functions (NEW: from landing.blade.php) ---
        function debounce(func, wait) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), wait);
            };
        }

        function setupScrollButtons(containerId, leftButtonId, rightButtonId) {
            const container = document.getElementById(containerId);
            const leftButton = document.getElementById(leftButtonId);
            const rightButton = document.getElementById(rightButtonId);
            if (!container || !leftButton || !rightButton) return;

            const checkScroll = () => {
                const isOverflowing = container.scrollWidth > container.clientWidth + 5;
                if (isOverflowing) {
                    leftButton.style.display = container.scrollLeft > 5 ? 'flex' : 'none';
                    rightButton.style.display = container.scrollLeft < (container.scrollWidth - container.clientWidth - 5) ? 'flex' : 'none';
                } else {
                    leftButton.style.display = 'none';
                    rightButton.style.display = 'none';
                }
            };

            leftButton.addEventListener('click', () => container.scrollBy({ left: -container.clientWidth * 0.8, behavior: 'smooth' }));
            rightButton.addEventListener('click', () => container.scrollBy({ left: container.clientWidth * 0.8, behavior: 'smooth' }));
            container.addEventListener('scroll', debounce(checkScroll, 50), { passive: true });
            window.addEventListener('resize', debounce(checkScroll, 150));

            // Initial check
            setTimeout(checkScroll, 500); // Check after content has rendered
        }

        function setupScrollDetection(container) {
            container.addEventListener('scroll', debounce(() => {
                if (isLoading || !hasMoreServices) return;
                const isNearEnd = container.scrollLeft + container.clientWidth >= container.scrollWidth - 300; // 300px threshold
                if (isNearEnd) {
                    loadMoreServices();
                }
            }, 100), { passive: true });
        }

        // --- Core Logic ---
        const categoryButtons = document.querySelectorAll('#service-categories .btn');
        categoryButtons.forEach(button => {
            button.addEventListener('click', function() {
                categoryButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                selectedCategory = this.getAttribute('data-category');

                // Filter currently visible items
                const serviceItems = document.querySelectorAll('.service-item');
                serviceItems.forEach(item => {
                    const itemCategory = item.getAttribute('data-category');
                    if (selectedCategory === 'all' || itemCategory === selectedCategory) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        function loadMoreServices() {
            if (isLoading || !hasMoreServices) return;
            isLoading = true;

            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'scroll-item';
            loadingIndicator.innerHTML = `<div id="loading-indicator" class="card"><div class="spinner-border text-maroon" role="status"></div></div>`;
            serviceCardsContainer.appendChild(loadingIndicator);

            let lat = localStorage.getItem('user_lat') || '{{ Session::get("user_location.lat") }}';
            let lng = localStorage.getItem('user_lng') || '{{ Session::get("user_location.lng") }}';

            fetch(`/api/nearby-services?page=${currentPage + 1}&category=${selectedCategory}&lat=${lat}&lng=${lng}`)
                .then(response => response.json())
                .then(data => {
                    serviceCardsContainer.removeChild(loadingIndicator);

                    if (data.services && data.services.length > 0) {
                        currentPage++;
                        data.services.forEach(service => {
                            const serviceCard = createServiceCard(service);
                            // Hide card if it doesn't match the current filter
                            if (selectedCategory !== 'all' && service.category_id != selectedCategory) {
                                serviceCard.style.display = 'none';
                            }
                            serviceCardsContainer.appendChild(serviceCard);
                        });
                        hasMoreServices = data.has_more;
                    } else {
                        hasMoreServices = false;
                    }
                    isLoading = false;
                })
                .catch(error => {
                    console.error('Error loading more services:', error);
                    serviceCardsContainer.removeChild(loadingIndicator);
                    isLoading = false;
                });
        }

        function createServiceCard(service) {
            const itemDiv = document.createElement('div');
            itemDiv.className = 'scroll-item service-item';
            itemDiv.setAttribute('data-category', service.categoryID || service.category_id || '');

            // Match the new card structure
            itemDiv.innerHTML = `
            <div class="service-card">
                <img src="${service.vendor_image || '{{ asset('assets/images/default-vendor.png') }}'}" alt="${service.serviceName || 'Service'}" class="service-image">
                <div class="service-details">
                    <div class="service-name">${service.serviceName || 'Service'}</div>
                    <div class="service-info">
                        Ksh ${new Intl.NumberFormat().format(service.price || 0)} for ${service.formatted_duration || ''}
                    </div>
                    <div class="service-meta">
                        <div class="rating">
                            <span>Pro: ${service.vendor_name || 'Vendor'}</span>
                            <i class="fa-solid fa-star ms-2"></i>
                            <span class="fw-bold">${(service.vendor_rating || 0).toFixed(1)}</span>
                            <span>(${service.review_count || 0} reviews)</span>
                        </div>
                        <div class="distance">
                            <i class="fa-solid fa-location-dot fa-xs me-1"></i>
                            <span>${service.vendor_distance || 'N/A'}</span>
                        </div>
                    </div>
                    <a href="/initiate/${service.vendor_id || ''}/${service.id || ''}" class="book-now-button">
                        BOOK NOW
                    </a>
                </div>
            </div>
        `;
            return itemDiv;
        }

        // --- Initial Setup ---
        setupScrollButtons('service-cards', 'nearby-services-left-btn', 'nearby-services-right-btn');
        setupScrollDetection(serviceCardsContainer);
    });

    // Banner Advertisement Functions
    let bannerManager = null;

    function loadBanners(lat, lng) {
        if (!bannerManager) {
            bannerManager = new BannerManager('banner-container');
        }
        bannerManager.loadBanners(lat, lng);
    }

    class BannerManager {
        constructor(containerId) {
            this.container = document.getElementById(containerId);
            this.banners = [];
            this.currentIndex = 0;
            this.autoSlideInterval = null;
            this.userLocation = { lat: null, lng: null };
            this.viewedBanners = new Set();
        }

        setUserLocation(lat, lng) {
            this.userLocation = { lat, lng };
        }

        async loadBanners(lat, lng) {
            try {
                this.setUserLocation(lat, lng);

                const response = await fetch(`/api/banners?lat=${lat}&lng=${lng}&limit=5`);
                const data = await response.json();

                if (data.success && data.banners && data.banners.length > 0) {
                    this.banners = data.banners;
                    this.renderBanners();
                    this.startAutoSlide();
                    this.showBannerContainer();
                } else {
                    this.hideBannerContainer();
                }

            } catch (error) {
                console.error('Error loading banners:', error);
                this.hideBannerContainer();
            }
        }

        async recordView(bannerId) {
            if (this.viewedBanners.has(bannerId)) return;

            try {
                const payload = {
                    lat: this.userLocation.lat,
                    lng: this.userLocation.lng
                };

                await fetch(`/api/banners/${bannerId}/view`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify(payload)
                });

                this.viewedBanners.add(bannerId);

            } catch (error) {
                console.error('Error recording banner view:', error);
            }
        }

        async recordClick(bannerId) {
            try {
                const payload = {
                    lat: this.userLocation.lat,
                    lng: this.userLocation.lng
                };

                await fetch(`/api/banners/${bannerId}/click`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify(payload)
                });

            } catch (error) {
                console.error('Error recording banner click:', error);
            }
        }

        renderBanners() {
            if (!this.container || this.banners.length === 0) return;

            this.container.innerHTML = `
                <div class="banner-carousel">
                    <div class="banner-slides">
                        ${this.banners.map((banner, index) => this.createBannerHTML(banner, index)).join('')}
                    </div>
                    ${this.banners.length > 1 ? this.createNavigationHTML() : ''}
                </div>
            `;

            this.setupEventListeners();
            this.showSlide(0);
        }

        createBannerHTML(banner, index) {
            const landscapeImage = banner.banner_landscape_image || '';
            const portraitImage = banner.banner_portrait_image || '';
            const bannerName = banner.banner_name || 'Advertisement';

            return `
                <div class="banner-slide" data-banner-id="${banner.id}" data-index="${index}">
                    <div class="banner-content" style="cursor: pointer;">
                        <img src="${landscapeImage}" alt="${bannerName}" class="banner-landscape d-none d-md-block">
                        <img src="${portraitImage}" alt="${bannerName}" class="banner-portrait d-md-none">
                        <div class="banner-overlay">
                            <span class="banner-label">Sponsored</span>
                        </div>
                    </div>
                </div>
            `;
        }

        createNavigationHTML() {
            return `
                <div class="banner-navigation">
                    <button class="banner-nav-btn banner-prev" aria-label="Previous banner">‹</button>
                    <div class="banner-dots">
                        ${this.banners.map((_, index) => `<span class="banner-dot" data-index="${index}"></span>`).join('')}
                    </div>
                    <button class="banner-nav-btn banner-next" aria-label="Next banner">›</button>
                </div>
            `;
        }

        setupEventListeners() {
            // Click handlers for banner content
            this.container.querySelectorAll('.banner-content').forEach((content, index) => {
                content.addEventListener('click', async () => {
                    const banner = this.banners[index];
                    await this.recordClick(banner.id);

                    // Navigate to vendor page
                    if (banner.vendor_id) {
                        window.open(`/vendor/${banner.vendor_id}`, '_blank');
                    }
                });
            });

            // Navigation handlers
            const prevBtn = this.container.querySelector('.banner-prev');
            const nextBtn = this.container.querySelector('.banner-next');

            if (prevBtn) prevBtn.addEventListener('click', () => this.prevSlide());
            if (nextBtn) nextBtn.addEventListener('click', () => this.nextSlide());

            // Dot navigation
            this.container.querySelectorAll('.banner-dot').forEach((dot, index) => {
                dot.addEventListener('click', () => this.showSlide(index));
            });

            // Intersection Observer for view tracking
            this.setupViewTracking();
        }

        setupViewTracking() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const bannerId = entry.target.dataset.bannerId;
                        if (bannerId) {
                            this.recordView(bannerId);
                        }
                    }
                });
            }, { threshold: 0.5 });

            this.container.querySelectorAll('.banner-slide').forEach(slide => {
                observer.observe(slide);
            });
        }

        showSlide(index) {
            const slides = this.container.querySelectorAll('.banner-slide');
            const dots = this.container.querySelectorAll('.banner-dot');

            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });

            dots.forEach((dot, i) => {
                dot.classList.toggle('active', i === index);
            });

            this.currentIndex = index;
        }

        nextSlide() {
            const nextIndex = (this.currentIndex + 1) % this.banners.length;
            this.showSlide(nextIndex);
        }

        prevSlide() {
            const prevIndex = (this.currentIndex - 1 + this.banners.length) % this.banners.length;
            this.showSlide(prevIndex);
        }

        startAutoSlide() {
            if (this.banners.length <= 1) return;

            this.autoSlideInterval = setInterval(() => {
                this.nextSlide();
            }, 5000);
        }

        stopAutoSlide() {
            if (this.autoSlideInterval) {
                clearInterval(this.autoSlideInterval);
                this.autoSlideInterval = null;
            }
        }

        showBannerContainer() {
            if (this.container) {
                this.container.style.display = 'block';
            }
        }

        hideBannerContainer() {
            if (this.container) {
                this.container.style.display = 'none';
            }
        }
    }
</script>
@endsection
